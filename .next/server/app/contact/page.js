(()=>{var e={};e.id=977,e.ids=[977],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3354:e=>{e.exports={contactPage:"page_contactPage__XDWjm",hero:"page_hero__0Vvk_",heroContent:"page_heroContent__EA5kU",heroDescription:"page_heroDescription__5ehzu",contactInfo:"page_contactInfo__jt5gr",contactGrid:"page_contactGrid__Kk0Sb",contactDetails:"page_contactDetails__V26_H",contactItem:"page_contactItem___gW29",contactIcon:"page_contactIcon__UuGMI",contactText:"page_contactText__yz7p5",contactLink:"page_contactLink__lXMS8",contactNote:"page_contactNote__Dp_Id",businessInfo:"page_businessInfo__wOb0X",hoursCard:"page_hoursCard__zQulN",servicesCard:"page_servicesCard__ZfMz_",hours:"page_hours__nd47z",hourItem:"page_hourItem__OlU5z",emergencyNote:"page_emergencyNote__uY3vE",servicesList:"page_servicesList__nPWE2",mapSection:"page_mapSection__TLm5V",mapContainer:"page_mapContainer__coTjG",mapPlaceholder:"page_mapPlaceholder__Llai5",serviceAreas:"page_serviceAreas__A_81o",areasList:"page_areasList__TqHZT",emergency:"page_emergency__Ff7Am",emergencyContent:"page_emergencyContent__LCiDN",cta:"page_cta__IExI2",ctaContent:"page_ctaContent__cJshf"}},3634:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>t});var n=s(1658);let t=async e=>[{type:"image/png",width:800,height:420,url:(0,n.fillMetadataSegment)(".",await e.params,"opengraph-image.png")+"?f10eaa3ae62961c0"}]},3873:e=>{"use strict";e.exports=require("path")},4555:()=>{},4650:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>t});var n=s(1658);let t=async e=>[{type:"image/png",sizes:"32x32",url:(0,n.fillMetadataSegment)(".",await e.params,"apple-icon.png")+"?e33ab4628b95696a"}]},6415:(e,a,s)=>{"use strict";s.d(a,{A:()=>i});var n=s(7413),t=s(7884),r=s.n(t);function i({layout:e="horizontal",size:a="default",showBooking:s=!0,className:t=""}){let i=`${r().ctaContainer} ${r()[e]} ${r()[a]} ${t}`;return(0,n.jsxs)("div",{className:i,children:[s&&(0,n.jsx)("a",{href:"/bookings",className:"btn btn-primary btn-large","aria-label":"Book an appointment",children:"Book Appointment"}),(0,n.jsx)("a",{href:"tel:+***********",className:"btn btn-large","aria-label":"Call Tuffside Automotive Garage",children:"\uD83D\uDCDE Call Now"}),(0,n.jsx)("a",{href:"https://wa.me/***********",target:"_blank",rel:"noopener noreferrer",className:"btn btn-large","aria-label":"Message us on WhatsApp",children:"\uD83D\uDCAC WhatsApp"})]})}},6987:(e,a,s)=>{"use strict";s.r(a),s.d(a,{GlobalError:()=>i.a,__next_app__:()=>h,pages:()=>d,routeModule:()=>p,tree:()=>o});var n=s(5239),t=s(8088),r=s(8170),i=s.n(r),c=s(893),l={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>c[e]);s.d(a,l);let o={children:["",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,7999)),"/Users/<USER>/Workspace/APP/Tuffside/src/app/contact/page.js"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,8162))).default(e)],apple:[async e=>(await Promise.resolve().then(s.bind(s,4650))).default(e)],openGraph:[async e=>(await Promise.resolve().then(s.bind(s,3634))).default(e)],twitter:[async e=>(await Promise.resolve().then(s.bind(s,7980))).default(e)],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,7779)),"/Users/<USER>/Workspace/APP/Tuffside/src/app/layout.js"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,8162))).default(e)],apple:[async e=>(await Promise.resolve().then(s.bind(s,4650))).default(e)],openGraph:[async e=>(await Promise.resolve().then(s.bind(s,3634))).default(e)],twitter:[async e=>(await Promise.resolve().then(s.bind(s,7980))).default(e)],manifest:void 0}}]}.children,d=["/Users/<USER>/Workspace/APP/Tuffside/src/app/contact/page.js"],h={require:s,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/contact/page",pathname:"/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},7523:()=>{},7884:e=>{e.exports={ctaContainer:"CTAButtons_ctaContainer__wa_Qd",horizontal:"CTAButtons_horizontal__YYOTX",vertical:"CTAButtons_vertical__sSNK2",default:"CTAButtons_default__vc_us",large:"CTAButtons_large__IH740",small:"CTAButtons_small__qJBJj",btn:"CTAButtons_btn__OqBds"}},7980:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>t});var n=s(1658);let t=async e=>[{type:"image/png",width:800,height:420,url:(0,n.fillMetadataSegment)(".",await e.params,"twitter-image.png")+"?f10eaa3ae62961c0"}]},7999:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>l,metadata:()=>c});var n=s(7413),t=s(6415),r=s(3354),i=s.n(r);let c={title:"Contact Tuffside Automotive Garage - Get in Touch",description:"Contact Tuffside Automotive Garage in Trinidad. Call ****** 335-7440, email <EMAIL>, or visit our location. Emergency services available 24/7."};function l(){return(0,n.jsxs)("div",{className:i().contactPage,children:[(0,n.jsx)("section",{className:i().hero,children:(0,n.jsx)("div",{className:"container",children:(0,n.jsxs)("div",{className:i().heroContent,children:[(0,n.jsx)("h1",{children:"Contact Us"}),(0,n.jsx)("p",{className:i().heroDescription,children:"Get in touch with Tuffside Automotive Garage. We're here to help with all your automotive needs, from routine maintenance to emergency repairs."})]})})}),(0,n.jsx)("section",{className:i().contactInfo,children:(0,n.jsx)("div",{className:"container",children:(0,n.jsxs)("div",{className:i().contactGrid,children:[(0,n.jsxs)("div",{className:i().contactDetails,children:[(0,n.jsx)("h2",{children:"Get in Touch"}),(0,n.jsxs)("div",{className:i().contactItem,children:[(0,n.jsx)("div",{className:i().contactIcon,children:"\uD83D\uDCDE"}),(0,n.jsxs)("div",{className:i().contactText,children:[(0,n.jsx)("h3",{children:"Phone"}),(0,n.jsx)("p",{children:(0,n.jsx)("a",{href:"tel:+***********",className:i().contactLink,children:"****** 335-7440"})}),(0,n.jsx)("span",{className:i().contactNote,children:"Available 24/7 for emergencies"})]})]}),(0,n.jsxs)("div",{className:i().contactItem,children:[(0,n.jsx)("div",{className:i().contactIcon,children:"✉️"}),(0,n.jsxs)("div",{className:i().contactText,children:[(0,n.jsx)("h3",{children:"Email"}),(0,n.jsx)("p",{children:(0,n.jsx)("a",{href:"mailto:<EMAIL>",className:i().contactLink,children:"<EMAIL>"})}),(0,n.jsx)("span",{className:i().contactNote,children:"We'll respond within 24 hours"})]})]}),(0,n.jsxs)("div",{className:i().contactItem,children:[(0,n.jsx)("div",{className:i().contactIcon,children:"\uD83D\uDCAC"}),(0,n.jsxs)("div",{className:i().contactText,children:[(0,n.jsx)("h3",{children:"WhatsApp"}),(0,n.jsx)("p",{children:(0,n.jsx)("a",{href:"https://wa.me/***********",target:"_blank",rel:"noopener noreferrer",className:i().contactLink,children:"Message us on WhatsApp"})}),(0,n.jsx)("span",{className:i().contactNote,children:"Quick responses during business hours"})]})]}),(0,n.jsxs)("div",{className:i().contactItem,children:[(0,n.jsx)("div",{className:i().contactIcon,children:"\uD83D\uDCCD"}),(0,n.jsxs)("div",{className:i().contactText,children:[(0,n.jsx)("h3",{children:"Location"}),(0,n.jsx)("p",{children:"Trinidad & Tobago"}),(0,n.jsx)("span",{className:i().contactNote,children:"Mobile service available island-wide"})]})]})]}),(0,n.jsxs)("div",{className:i().businessInfo,children:[(0,n.jsxs)("div",{className:i().hoursCard,children:[(0,n.jsx)("h3",{children:"Business Hours"}),(0,n.jsxs)("div",{className:i().hours,children:[(0,n.jsxs)("div",{className:i().hourItem,children:[(0,n.jsx)("span",{children:"Monday - Friday"}),(0,n.jsx)("span",{children:"8:00 AM - 6:00 PM"})]}),(0,n.jsxs)("div",{className:i().hourItem,children:[(0,n.jsx)("span",{children:"Saturday"}),(0,n.jsx)("span",{children:"8:00 AM - 4:00 PM"})]}),(0,n.jsxs)("div",{className:i().hourItem,children:[(0,n.jsx)("span",{children:"Sunday"}),(0,n.jsx)("span",{children:"Emergency Only"})]})]}),(0,n.jsx)("p",{className:i().emergencyNote,children:"\uD83D\uDEA8 Emergency services available 24/7"})]}),(0,n.jsxs)("div",{className:i().servicesCard,children:[(0,n.jsx)("h3",{children:"Quick Services"}),(0,n.jsxs)("ul",{className:i().servicesList,children:[(0,n.jsx)("li",{children:"Engine Diagnostics"}),(0,n.jsx)("li",{children:"Diesel Repair"}),(0,n.jsx)("li",{children:"Engine Tuning"}),(0,n.jsx)("li",{children:"Suspension Work"}),(0,n.jsx)("li",{children:"Electrical Systems"}),(0,n.jsx)("li",{children:"General Maintenance"}),(0,n.jsx)("li",{children:"Emergency Roadside"})]})]})]})]})})}),(0,n.jsx)("section",{className:i().mapSection,children:(0,n.jsxs)("div",{className:"container",children:[(0,n.jsx)("h2",{className:"text-center mb-lg",children:"Our Service Area"}),(0,n.jsx)("div",{className:i().mapContainer,children:(0,n.jsxs)("div",{className:i().mapPlaceholder,children:[(0,n.jsx)("h3",{children:"\uD83D\uDDFA️ Trinidad & Tobago"}),(0,n.jsx)("p",{children:"Island-wide mobile service available"}),(0,n.jsx)("p",{children:"Emergency response across all regions"})]})}),(0,n.jsxs)("div",{className:i().serviceAreas,children:[(0,n.jsx)("h4",{children:"We Service All Areas Including:"}),(0,n.jsxs)("div",{className:i().areasList,children:[(0,n.jsx)("span",{children:"Port of Spain"}),(0,n.jsx)("span",{children:"San Fernando"}),(0,n.jsx)("span",{children:"Chaguanas"}),(0,n.jsx)("span",{children:"Arima"}),(0,n.jsx)("span",{children:"Point Fortin"}),(0,n.jsx)("span",{children:"Couva"}),(0,n.jsx)("span",{children:"Marabella"}),(0,n.jsx)("span",{children:"Sangre Grande"}),(0,n.jsx)("span",{children:"And surrounding areas"})]})]})]})}),(0,n.jsx)("section",{className:i().emergency,children:(0,n.jsx)("div",{className:"container",children:(0,n.jsxs)("div",{className:i().emergencyContent,children:[(0,n.jsx)("h2",{children:"\uD83D\uDEA8 Emergency Breakdown?"}),(0,n.jsx)("p",{children:"Don't panic! We provide 24/7 emergency roadside assistance across Trinidad & Tobago. Call us now for immediate help."}),(0,n.jsx)("a",{href:"tel:+***********",className:"btn btn-primary btn-large",children:"\uD83D\uDCDE Emergency Call: ****** 335-7440"})]})})}),(0,n.jsx)("section",{className:i().cta,children:(0,n.jsx)("div",{className:"container",children:(0,n.jsxs)("div",{className:i().ctaContent,children:[(0,n.jsx)("h2",{children:"Ready to Get Started?"}),(0,n.jsx)("p",{children:"Contact us today to schedule your service, get a quote, or ask any questions. We're here to help keep you moving!"}),(0,n.jsx)(t.A,{layout:"horizontal",size:"large"})]})})})]})}},8162:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>t});var n=s(1658);let t=async e=>[{type:"image/png",sizes:"32x32",url:(0,n.fillMetadataSegment)(".",await e.params,"icon.png")+"?e33ab4628b95696a"}]},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var a=require("../../webpack-runtime.js");a.C(e);var s=e=>a(a.s=e),n=a.X(0,[447,55,658,29],()=>s(6987));module.exports=n})();