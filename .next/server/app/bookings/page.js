(()=>{var e={};e.id=407,e.ids=[407],e.modules={631:(e,r,n)=>{"use strict";n.r(r),n.d(r,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>l,routeModule:()=>u,tree:()=>d});var a=n(5239),i=n(8088),t=n(8170),o=n.n(t),s=n(893),c={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>s[e]);n.d(r,c);let d={children:["",{children:["bookings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,3741)),"/Users/<USER>/Workspace/APP/Tuffside/src/app/bookings/page.js"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,8162))).default(e)],apple:[async e=>(await Promise.resolve().then(n.bind(n,4650))).default(e)],openGraph:[async e=>(await Promise.resolve().then(n.bind(n,3634))).default(e)],twitter:[async e=>(await Promise.resolve().then(n.bind(n,7980))).default(e)],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(n.bind(n,7779)),"/Users/<USER>/Workspace/APP/Tuffside/src/app/layout.js"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(n.t.bind(n,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,8162))).default(e)],apple:[async e=>(await Promise.resolve().then(n.bind(n,4650))).default(e)],openGraph:[async e=>(await Promise.resolve().then(n.bind(n,3634))).default(e)],twitter:[async e=>(await Promise.resolve().then(n.bind(n,7980))).default(e)],manifest:void 0}}]}.children,l=["/Users/<USER>/Workspace/APP/Tuffside/src/app/bookings/page.js"],p={require:n,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/bookings/page",pathname:"/bookings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3255:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3634:(e,r,n)=>{"use strict";n.r(r),n.d(r,{default:()=>i});var a=n(1658);let i=async e=>[{type:"image/png",width:800,height:420,url:(0,a.fillMetadataSegment)(".",await e.params,"opengraph-image.png")+"?f10eaa3ae62961c0"}]},3741:(e,r,n)=>{"use strict";n.r(r),n.d(r,{default:()=>l,metadata:()=>d});var a=n(7413),i=n(8738),t=n.n(i);function o({title:e,duration:r,description:n,bookingUrl:i,icon:o,urgent:s=!1}){let c=`${t().bookingCard} ${s?t().urgent:""}`;return(0,a.jsxs)("div",{className:c,children:[(0,a.jsxs)("div",{className:t().cardHeader,children:[(0,a.jsx)("div",{className:t().cardIcon,children:o}),(0,a.jsxs)("div",{className:t().cardTitle,children:[(0,a.jsx)("h3",{children:e}),(0,a.jsxs)("span",{className:t().duration,children:["Duration: ",r]})]})]}),(0,a.jsx)("div",{className:t().cardContent,children:(0,a.jsx)("p",{className:t().description,children:n})}),(0,a.jsx)("div",{className:t().cardFooter,children:(0,a.jsx)("a",{href:i,target:"_blank",rel:"noopener noreferrer",className:`btn ${s?"btn-primary":""} btn-large ${t().bookingButton}`,children:"Book Now"})}),s&&(0,a.jsx)("div",{className:t().urgentBadge,children:"Emergency Service"})]})}var s=n(8e3),c=n.n(s);let d={title:"Book a Service - Tuffside Automotive Garage",description:"Book your automotive service online. Choose from emergency services, repairs, or general servicing. Easy online booking with TidyCal."};function l(){return(0,a.jsxs)("div",{className:c().bookingsPage,children:[(0,a.jsx)("section",{className:c().hero,children:(0,a.jsx)("div",{className:"container",children:(0,a.jsxs)("div",{className:c().heroContent,children:[(0,a.jsx)("h1",{children:"Book a Service"}),(0,a.jsx)("p",{className:c().heroDescription,children:"Choose the service that best fits your needs and book online. We'll confirm your appointment and get your vehicle back in top shape."})]})})}),(0,a.jsx)("section",{className:c().bookingCards,children:(0,a.jsx)("div",{className:"container",children:(0,a.jsx)("div",{className:c().cardsGrid,children:[{title:"Emergency or On-site Services",duration:"1 hour",description:"Stuck on the road or need help at your location? We offer call-outs for breakdowns, urgent repairs, and vehicle recovery. Whether it's a dead battery, a flat tyre, or something more involved, we're equipped to respond quickly and get you moving again.",bookingUrl:"https://tidycal.com/1w7lwem/emergency-or-on-site-services",icon:"\uD83D\uDEA8",urgent:!0},{title:"Repairs",duration:"15 minutes",description:"Whether it's something minor or a bit more serious, we'll take a thorough look and get your vehicle back in shape. This includes engine diagnostics, suspension issues, brake repairs, electrical faults, and other mechanical concerns. Bring it in—we'll sort it out.",bookingUrl:"https://tidycal.com/1w7lwem/repairs",icon:"\uD83D\uDD27",urgent:!1},{title:"General Servicing",duration:"2 hours",description:"Keep your vehicle running smoothly with a routine check-up. This service covers a standard maintenance checklist including oil and filter changes, fluid top-ups, brake inspection, tyre rotation, battery health, and more. Ideal for regular upkeep and peace of mind.",bookingUrl:"https://tidycal.com/1w7lwem/general-servicing",icon:"\uD83D\uDEE0️",urgent:!1}].map((e,r)=>(0,a.jsx)(o,{title:e.title,duration:e.duration,description:e.description,bookingUrl:e.bookingUrl,icon:e.icon,urgent:e.urgent},r))})})}),(0,a.jsx)("section",{className:c().bookingInfo,children:(0,a.jsx)("div",{className:"container",children:(0,a.jsxs)("div",{className:c().infoContent,children:[(0,a.jsxs)("div",{className:c().infoText,children:[(0,a.jsx)("h2",{children:"How It Works"}),(0,a.jsxs)("ol",{className:c().stepsList,children:[(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"Choose Your Service:"})," Select the type of service that best matches your needs from the options above."]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"Pick Your Time:"})," Choose a convenient date and time from our available slots."]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"Provide Details:"})," Tell us about your vehicle and any specific concerns you have."]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"Confirmation:"})," We'll confirm your booking and send you all the details you need."]})]})]}),(0,a.jsxs)("div",{className:c().contactInfo,children:[(0,a.jsx)("h3",{children:"Need Help Booking?"}),(0,a.jsx)("p",{children:"If you're not sure which service you need or prefer to book over the phone, we're here to help."}),(0,a.jsxs)("div",{className:c().contactOptions,children:[(0,a.jsx)("a",{href:"tel:+18683357440",className:"btn btn-primary",children:"\uD83D\uDCDE Call Us"}),(0,a.jsx)("a",{href:"https://wa.me/18683357440",target:"_blank",rel:"noopener noreferrer",className:"btn",children:"\uD83D\uDCAC WhatsApp"}),(0,a.jsx)("a",{href:"mailto:<EMAIL>",className:"btn",children:"✉️ Email Us"})]})]})]})})}),(0,a.jsx)("section",{className:c().emergency,children:(0,a.jsx)("div",{className:"container",children:(0,a.jsxs)("div",{className:c().emergencyContent,children:[(0,a.jsx)("h2",{children:"\uD83D\uDEA8 Emergency Breakdown?"}),(0,a.jsx)("p",{children:"If you're currently broken down or need immediate assistance, don't wait for an appointment - call us now!"}),(0,a.jsx)("a",{href:"tel:+18683357440",className:"btn btn-primary btn-large",children:"\uD83D\uDCDE Emergency Call: ****** 335-7440"})]})})})]})}},3873:e=>{"use strict";e.exports=require("path")},4650:(e,r,n)=>{"use strict";n.r(r),n.d(r,{default:()=>i});var a=n(1658);let i=async e=>[{type:"image/png",sizes:"32x32",url:(0,a.fillMetadataSegment)(".",await e.params,"apple-icon.png")+"?e33ab4628b95696a"}]},7167:()=>{},7980:(e,r,n)=>{"use strict";n.r(r),n.d(r,{default:()=>i});var a=n(1658);let i=async e=>[{type:"image/png",width:800,height:420,url:(0,a.fillMetadataSegment)(".",await e.params,"twitter-image.png")+"?f10eaa3ae62961c0"}]},8e3:e=>{e.exports={bookingsPage:"page_bookingsPage__ljLgN",hero:"page_hero__c0n1a",heroContent:"page_heroContent__seQKr",heroDescription:"page_heroDescription__iDNso",bookingCards:"page_bookingCards__KkYNQ",cardsGrid:"page_cardsGrid__6AX3m",bookingInfo:"page_bookingInfo__J7Pur",infoContent:"page_infoContent__aEVv3",infoText:"page_infoText__ourpa",stepsList:"page_stepsList__MDlBI",contactInfo:"page_contactInfo__VQAOc",contactOptions:"page_contactOptions__XS_NU",emergency:"page_emergency__B_Jfx",emergencyContent:"page_emergencyContent__c4s67"}},8162:(e,r,n)=>{"use strict";n.r(r),n.d(r,{default:()=>i});var a=n(1658);let i=async e=>[{type:"image/png",sizes:"32x32",url:(0,a.fillMetadataSegment)(".",await e.params,"icon.png")+"?e33ab4628b95696a"}]},8738:e=>{e.exports={bookingCard:"BookingCard_bookingCard__WuUmf",urgent:"BookingCard_urgent__pCzbB",cardHeader:"BookingCard_cardHeader__ciU57",cardIcon:"BookingCard_cardIcon__6nQbt",cardTitle:"BookingCard_cardTitle__oTzgC",duration:"BookingCard_duration__Cdcue",cardContent:"BookingCard_cardContent__ndqG9",description:"BookingCard_description__tobEJ",cardFooter:"BookingCard_cardFooter__bMU9z",bookingButton:"BookingCard_bookingButton__aurgb",urgentBadge:"BookingCard_urgentBadge__PJOlj",pulse:"BookingCard_pulse__0oHHt"}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var r=require("../../webpack-runtime.js");r.C(e);var n=e=>r(r.s=e),a=r.X(0,[447,55,658,29],()=>n(631));module.exports=a})();