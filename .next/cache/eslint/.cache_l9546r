[{"/Users/<USER>/Workspace/APP/Tuffside/src/app/about/page.js": "1", "/Users/<USER>/Workspace/APP/Tuffside/src/app/bookings/page.js": "2", "/Users/<USER>/Workspace/APP/Tuffside/src/app/contact/page.js": "3", "/Users/<USER>/Workspace/APP/Tuffside/src/app/layout.js": "4", "/Users/<USER>/Workspace/APP/Tuffside/src/app/page.js": "5", "/Users/<USER>/Workspace/APP/Tuffside/src/app/services/page.js": "6", "/Users/<USER>/Workspace/APP/Tuffside/src/app/testimonials/page.js": "7", "/Users/<USER>/Workspace/APP/Tuffside/src/components/BookingCard.js": "8", "/Users/<USER>/Workspace/APP/Tuffside/src/components/CTAButtons.js": "9", "/Users/<USER>/Workspace/APP/Tuffside/src/components/Footer.js": "10", "/Users/<USER>/Workspace/APP/Tuffside/src/components/Header.js": "11"}, {"size": 6486, "mtime": 1748652925968, "results": "12", "hashOfConfig": "13"}, {"size": 5621, "mtime": 1748653005750, "results": "14", "hashOfConfig": "13"}, {"size": 7050, "mtime": 1748653113927, "results": "15", "hashOfConfig": "13"}, {"size": 3378, "mtime": 1748797762002, "results": "16", "hashOfConfig": "13"}, {"size": 4678, "mtime": 1748790674115, "results": "17", "hashOfConfig": "13"}, {"size": 6465, "mtime": 1748791226941, "results": "18", "hashOfConfig": "13"}, {"size": 6507, "mtime": 1748791605203, "results": "19", "hashOfConfig": "13"}, {"size": 1122, "mtime": 1748653014298, "results": "20", "hashOfConfig": "13"}, {"size": 970, "mtime": 1748652842212, "results": "21", "hashOfConfig": "13"}, {"size": 3574, "mtime": 1748652815923, "results": "22", "hashOfConfig": "13"}, {"size": 8693, "mtime": 1748793178334, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1vp79c5", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Workspace/APP/Tuffside/src/app/about/page.js", [], [], "/Users/<USER>/Workspace/APP/Tuffside/src/app/bookings/page.js", [], [], "/Users/<USER>/Workspace/APP/Tuffside/src/app/contact/page.js", [], [], "/Users/<USER>/Workspace/APP/Tuffside/src/app/layout.js", [], [], "/Users/<USER>/Workspace/APP/Tuffside/src/app/page.js", [], [], "/Users/<USER>/Workspace/APP/Tuffside/src/app/services/page.js", [], [], "/Users/<USER>/Workspace/APP/Tuffside/src/app/testimonials/page.js", [], [], "/Users/<USER>/Workspace/APP/Tuffside/src/components/BookingCard.js", [], [], "/Users/<USER>/Workspace/APP/Tuffside/src/components/CTAButtons.js", [], [], "/Users/<USER>/Workspace/APP/Tuffside/src/components/Footer.js", [], [], "/Users/<USER>/Workspace/APP/Tuffside/src/components/Header.js", [], []]